package com.nymbl.tenant.controller;

import com.nymbl.config.controller.AbstractController;
import com.nymbl.config.dto.BranchDTO;
import com.nymbl.config.security.TokenUtils;
import com.nymbl.config.service.BranchLogoService;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.model.Company;
import com.nymbl.master.service.CompanyService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.model.Branch;
import com.nymbl.tenant.service.BranchService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Bradley Moore on 05/21/2017.
 */
@RestController
@RequestMapping("/api/branch")
public class BranchController extends AbstractController<Branch, Long> {

    private final BranchService branchService;
    private final BranchLogoService branchLogoService;
    private final UserService userService;
    private final CompanyService companyService;
    private final TokenUtils tokenUtils;

    @Autowired
    public BranchController(BranchService branchService,
                            BranchLogoService branchLogoService,
                            UserService userService,
                            CompanyService companyService,
                            TokenUtils tokenUtils) {
        super(branchService);
        this.branchService = branchService;
        this.branchLogoService = branchLogoService;
        this.userService = userService;
        this.companyService = companyService;
        this.tokenUtils = tokenUtils;
    }

    /**
     * Get branches by keyword and active flag (can be null)
     *
     * @param keyword keyword(s) to search
     * @param active true/false/null
     * @return List of branches based on param values
     *
     */
//    @Auditable(entry = "Search Branch")
    @GetMapping(value = "/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> search(@RequestParam(name = "q", required = false) String keyword,
                                    @RequestParam(name = "active", required = false) Boolean active,
                                    HttpServletRequest request) {
        if(TenantContext.getCurrentTenant() == null ||
                "".equals(TenantContext.getCurrentTenant()) ||
                TenantContext.DEFAULT_TENANT.equals(TenantContext.getCurrentTenant()))
            return ResponseEntity.ok(null);
        List<Branch> results = branchService.search(keyword, active);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/active", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> active(HttpServletRequest request) {
        if (TenantContext.getCurrentTenant() == null ||
            "".equals(TenantContext.getCurrentTenant()) ||
            TenantContext.DEFAULT_TENANT.equals(TenantContext.getCurrentTenant())) {
            return ResponseEntity.ok(null);
        }
        List<Branch> results = branchService.findAllByActiveTrue();
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/dto/{branchId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getDTO(@PathVariable Long branchId, HttpServletRequest request) throws Exception {
        BranchDTO dto = new BranchDTO();
        Branch branch = branchService.findOne(branchId);
        Long companyId = userService.getCurrentCompany().getId();
        String branchLogo = branchLogoService.getBranchLogo(branchId, companyId);
        dto.setBranch(branch);
        dto.setBranchLogo(branchLogo);
        return ResponseEntity.ok(dto);
    }

    @PostMapping(value = "/dto", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> saveDTO(@Valid @RequestBody BranchDTO dto, HttpServletRequest request) throws Exception {
        Branch branch = dto.getBranch();
        String branchLogoString = dto.getBranchLogo();
        Long companyId = userService.getCurrentCompany().getId();
        branchService.save(branch);
        if (!StringUtil.isBlank(branchLogoString) && branchLogoString.contains("img")) {
            branchLogoService.saveCompanyBranchLogoImage(branchLogoString, companyId, branch.getId());
        } else if (!StringUtil.isBlank(branchLogoString)) {
            branchLogoService.saveCompanyBranchLogoText(branchLogoString, companyId, branch.getId());
        } else {
            branchLogoService.deleteCompanyBranchLogo(branch.getId(), companyId);
        }
        return ResponseEntity.ok(dto);
    }

    @GetMapping(value = "/change-active/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void changeBranch(@PathVariable Long id, HttpServletRequest request, HttpServletResponse response) {
        Company c = companyService.findByKey(TenantContext.getCurrentTenant());
        String token = tokenUtils.generateToken(userService.getCurrentUser(), c.getId(), id);
        Cookie tokenCookie = this.tokenUtils.tokenCookie(token);
        response.addCookie(tokenCookie);
    }

    @DeleteMapping(value = "/delete/{branchId}")
    public ResponseEntity<?> deleteBranch(@PathVariable Long branchId, HttpServletRequest request) throws Exception {
        try {
            branchService.delete(branchId);
        } catch (Exception e) {
            return handleDeleteException(e);
        }
        return ResponseEntity.noContent().build();
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<?> handleBranchConstraint(DataIntegrityViolationException e) {
        String message = e.getMostSpecificCause().getMessage();
        Map<String, Map<String, String>> errors = new HashMap<>();
        if (e.getMostSpecificCause() instanceof SQLIntegrityConstraintViolationException) {
            if (message.contains("UK_branch_name") || message.contains("name")) {
                message = "Branch name already exists. Please choose a different name.";
                Map<String, String> branchName = new HashMap<>();
                branchName.put("name", message);
                errors.put("errors", branchName);
            }
        }

        if (!errors.isEmpty()) return ResponseEntity.badRequest().body(errors);

        return ResponseEntity.badRequest().body(message);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<?> handleBranchValidation(ConstraintViolationException e) {
        Map<String, String> errors =
            e.getConstraintViolations().stream()
                .collect(
                    Collectors.toMap(
                        violation -> violation.getPropertyPath().toString(),
                        ConstraintViolation::getMessage,
                        (message1, message2) -> message1 + ", " + message2));
        Map<String, Map<String, String>> errorsObjectReturned = new HashMap<>();
        errorsObjectReturned.put("errors", errors);
        return ResponseEntity.badRequest().body(errorsObjectReturned);
    }
}
