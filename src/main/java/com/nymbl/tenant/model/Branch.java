package com.nymbl.tenant.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nymbl.tenant.model.interfaces.BillingInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by <PERSON> on 05/16/2017.
 */
@Getter
@Setter
@Entity
@Table(name = "branch", uniqueConstraints = @UniqueConstraint(name = "UK_branch_name", columnNames = "name"))
@Audited
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id", "active", "name", "tagLine", "npi", "otherId1", "otherId2", "streetAddress", "city", "state", "zipcode",
        "phoneNumber", "faxNumber", "billingCompanyName", "billingStreetAddress", "billingCity", "billingState",
        "billingZipcode", "billingPhoneNumber", "billingFaxNumber", "useSalesTax", "salesTax", "projectedDeliveryOffset",
        "monthlySalesGoal", "glAccount"
})
@JsonIgnoreProperties(ignoreUnknown = true)
public class Branch implements Serializable, BillingInfo {

    @Serial
    private static final long serialVersionUID = 1L;

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    @Column(name = "active", columnDefinition = "TINYINT(1) DEFAULT '1'")
    private Boolean active;

    @Pattern(regexp = "^[a-zA-Z0-9\\s\\-_.,()&]+$", message = "Branch name contains invalid characters")
    @Column(name = "name", length = 100, unique = true, nullable = false)
    private String name;

    @Column(name = "code", length = 3)
    private String code;

    @Column(name = "tag_line", length = 100)
    private String tagLine;

    @Column(name = "npi", length = 15)
    private String npi;

    @Column(name = "other_id1", length = 15)
    private String otherId1;

    @Column(name = "other_id2", length = 15)
    private String otherId2;

    @Column(name = "street_address", length = 100)
    private String streetAddress;

    @Column(name = "po_box", length = 100)
    private String poBox;

    @Column(name = "city", length = 30)
    private String city;

    @Column(name = "state", length = 2)
    private String state;

    @Column(name = "zipcode", length = 10)
    private String zipcode;

    @Column(name = "country", length = 50)
    private String country;

    @Column(name = "phone_number", length = 25)
    private String phoneNumber;

    @Column(name = "fax_number", length = 25)
    private String faxNumber;

    @Column(name = "billing_company_name", length = 100)
    private String billingCompanyName;

    @Column(name = "billing_street_address", length = 100)
    private String billingStreetAddress;

    @Column(name = "billing_city", length = 30)
    private String billingCity;

    @Column(name = "billing_state", length = 2)
    private String billingState;

    @Column(name = "billing_zipcode", length = 30)
    private String billingZipcode;

    @Column(name = "billing_phone_number", length = 25)
    private String billingPhoneNumber;

    @Column(name = "billing_fax_number", length = 25)
    private String billingFaxNumber;

    @Column(name = "billing_npi", length = 15)
    private String billingNpi;

    @Deprecated
    @Column(name = "zirmed_host_url", length = 200, nullable = false)
    private String zirmedHostUrl;

    @Deprecated
    @Column(name = "zirmed_account_number", length = 50, nullable = false)
    private String zirmedAccountNumber;

    @Deprecated
    @Column(name = "zirmed_password", length = 50, nullable = false)
    private String zirmedPassword;

    @Deprecated
    @Column(name = "zirmed_hmac_key", length = 50, nullable = false)
    private String zirmedHmacKey;

    @Deprecated
    @Column(name = "zirmed_active", columnDefinition = "TINYINT(1) DEFAULT '1'")
    private Boolean zirmedActive;

    @Deprecated
    @Column(name = "zirmed_rest_user", length = 50)
    private String zirmedRestUser;

    @Deprecated
    @Column(name = "zirmed_rest_password", length = 50)
    private String zirmedRestPassword;

    @Deprecated
    @Column(name = "waystar_override_pay_to", columnDefinition = "TINYINT(1) DEFAULT '0'")
    private Boolean waystarOverridePayTo;

    @Column(name = "send_notifications", columnDefinition = "TINYINT(1) DEFAULT '1'")
    private Boolean sendNotifications;

    @Column(name = "additional_text", columnDefinition = "text")
    private String additionalText;

    @Column(name = "tax_id", length = 40)
    private String taxId;

    @Column(name = "tax_id_type", length = 40, columnDefinition = "VARCHAR(40) DEFAULT 'leave_blank'")
    private String taxIdType;

    @Column(name = "hide_company_name", columnDefinition = "TINYINT(1) DEFAULT '0'")
    private Boolean hideCompanyName;

    @Column(name = "hide_company_logo", columnDefinition = "TINYINT(1) DEFAULT '0'")
    private Boolean hideCompanyLogo;

    @Column(name = "hide_service_facility_location", columnDefinition = "TINYINT(1) DEFAULT '0'")
    private Boolean hideServiceFacilityLocation;

    @Column(name = "use_branch_logo", columnDefinition = "TINYINT(1) DEFAULT '0'")
    private Boolean useBranchLogo;

    @Column(name = "use_branch_name", columnDefinition = "TINYINT(1) DEFAULT '0'")
    private Boolean useBranchName;

    @Column(name = "use_sales_tax", columnDefinition = "TINYINT(1) DEFAULT '0'")
    private Boolean useSalesTax;

    @Column(name = "sales_tax_calculation_value", length = 10)
    private String salesTaxCalculationValue;

    @Column(name = "pay_online_url", length = 500)
    private String payOnlineUrl;

    @Column(name = "sales_tax")
    private BigDecimal salesTax;

    @Column(name = "projected_delivery_offset")
    private Long projectedDeliveryOffset;

    @Column(name = "monthly_sales_goal")
    private BigDecimal monthlySalesGoal;

    @Column(name = "gl_account", length = 45)
    private String glAccount;

    @Column(name = "parent_id")
    private Long parentId;

    @Deprecated
    @Column(name = "clearing_house_id")
    private Long clearingHouseId;

    @Column(name = "in_clearing_house_id")
    private Long inClearingHouseId;

    @Column(name = "out_clearing_house_id")
    private Long outClearingHouseId;

    @Column(name = "stripe_account_id")
    private String stripeAccountId;

    @Column(name = "use_real_time_rules_engine", columnDefinition = "TINYINT(1) DEFAULT '0'")
    private Boolean useRealTimeRulesEngine;

    @Column(name = "stripe_location_id")
    private String stripeLocationId;

    @Column(name = "po_prefix")
    private String purchaseOrderPrefix;

    @Column(name = "timezone")
    private String timezone;

    @Column(name = "source_name", length = 100)
    private String sourceName;

    @Column(name = "source_id")
    private Long sourceId;

    @Column(name = "billing_email")
    private String billingEmail;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "parent_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_branch_parentid"))
    private Branch parent;

//    @Deprecated
//    @NotAudited
//    @ManyToOne
//    @JoinColumn(name = "clearing_house_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_branch_clearinghouseid"))
//    private ClearingHouse clearingHouse;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "in_clearing_house_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_branch_inclearinghouseid"))
    private ClearingHouse inClearingHouse;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "out_clearing_house_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_branch_outclearinghouseid"))
    private ClearingHouse outClearingHouse;

    @Override
    public String toString() {
        return name;
    }
}
