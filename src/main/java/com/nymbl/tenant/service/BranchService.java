package com.nymbl.tenant.service;

import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.model.PostalCodeTimezone;
import com.nymbl.master.repository.PostalCodeTimezoneRepository;
import com.nymbl.tenant.model.Branch;
import com.nymbl.tenant.repository.BranchRepository;
import com.nymbl.tenant.repository.ItemRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers.contains;
import static org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers.exact;

/**
 * Created by vbi on 6/16/17.
 */
@Slf4j
@Service
public class BranchService extends AbstractTableService<Branch, Long> {

    private final BranchRepository branchRepository;
    private final ItemRepository itemRepository;

    private final PostalCodeTimezoneRepository postalCodeTimezoneRepository;
    private final InventoryItemService inventoryItemService;

    @PersistenceContext(unitName = "tenant")
    private EntityManager entityManager;

    @Autowired
    public BranchService(BranchRepository branchRepository, ItemRepository itemRepository,
                         PostalCodeTimezoneRepository postalCodeTimezoneRepository, InventoryItemService inventoryItemService) {
        super(branchRepository);
        this.branchRepository = branchRepository;
        this.itemRepository = itemRepository;
        this.postalCodeTimezoneRepository = postalCodeTimezoneRepository;
        this.inventoryItemService = inventoryItemService;
    }

    /**
     * Search branches by keyword and active flag (can be null)
     *
     * @param keyword keyword(s) to search
     * @param active  true/false/null
     * @return List of branches based on param values.<br>
     * If specification is null, return the whole table.
     */
    public List<Branch> search(String keyword, Boolean active) {
        Pageable pageable = makePageable(Sort.Direction.ASC, "name", 0, 1000);
        Page<Branch> result = null;
        Example<Branch> example = null;
        if (!StringUtil.isBlank(keyword) || active != null) {
            Branch branch = new Branch();
            ExampleMatcher matcher = ExampleMatcher.matching();
            if (!StringUtil.isBlank(keyword)) {
                branch.setName(keyword);
                matcher = matcher.withMatcher("name", contains().ignoreCase());
            }
            if (active != null) {
                branch.setActive(active);
                branch.setTaxIdType(null);
                matcher = matcher.withMatcher("active", exact());
            }
            example = Example.of(branch, matcher);
        }
        if (example != null) {
            result = branchRepository.findAll(example, pageable);
//        Specification<Branch> spec = BranchSpecs.branchByKeywordsAndActive(keyword, active);
//        if(spec != null {
//            result = branchRepository.findAll(spec, pageable);
        } else {
            result = branchRepository.findAll(pageable);
        }
        return result == null ? Collections.emptyList() : result.getContent();
    }

    @Override
//    @Caching(
//            evict = {
//                    @CacheEvict(value = "branches", beforeInvocation = true, allEntries = true),
//                    @CacheEvict(value = "unknown_branches", beforeInvocation = true, allEntries = true)
//            })
    public Branch save(Branch branch) {
        Boolean newRecord = branch.getId() == 0;

        // Sanitize branch name to prevent SQL injection and XSS
        if (branch.getName() != null) {
            branch.setName(StringUtil.sanitizeInput(branch.getName()));
        }

        if (branch.getZipcode() != null) {
            PostalCodeTimezone postalCodeTimezone = postalCodeTimezoneRepository.findByPostalCode(branch.getZipcode().substring(0, Math.min(branch.getZipcode().length(), 5)));
            branch.setTimezone(postalCodeTimezone != null ? postalCodeTimezone.getTimezone() : null);
        }
        Branch result = branchRepository.save(branch);
        loadForeignKeys(result);
        if (newRecord) inventoryItemService.populateNewBranchInventory(result.getId());
        return result;
    }

    public List<Branch> findAllByOutClearingHouseActiveTrue() {
        return branchRepository.findAllByActiveTrueAndOutClearingHouseActiveTrue();
    }

    public List<Branch> findAllByInClearingHouseActiveTrue() {
        return branchRepository.findAllByActiveTrueAndInClearingHouseActiveTrue();
    }

    public List<Branch> findAllByActiveTrue() {
//        if(TenantContext.getCurrentTenant().equals("unknown")) {
//            Sentry.captureMessage("SCRUM-1864: Tenant is unknown--explore");
//        }
        return branchRepository.findAllByActiveTrue();
    }

    public List<Branch> findAllByCity(String city) {
        return branchRepository.findAllByCity(city);
    }

    public List<Long> findAllBranchIdsByName(String name) {
        return branchRepository.findAllBranchIdsByName(name);
    }

    public List<Long> findAllIdsByActiveFalse() {
        return branchRepository.findAllIdsByActiveFalse();
    }

    public List<Long> findAllIdsByActiveTrue() {
        return branchRepository.findAllIdsByActiveTrue();
    }

    public Branch findByName(String name) {
        return branchRepository.findByName(name);
    }

    public List<Long> getChildrenIdsByParentIds(List<Long> branchIds, boolean activeOnly) {
        String sql = "SELECT `id` FROM `branch` WHERE `parent_id` IN (:parentIds)";
        if (activeOnly) {
            sql += " AND `active` = 1";
        }
        List<Long> children = entityManager.createNativeQuery(sql)
                .setParameter("parentIds", branchIds)
                .getResultList();
        for (Long id : branchIds) {
            if (!children.contains(id)) {
                children.add(id);
            }
        }
        if (children.size() > branchIds.size()) {
            children = getChildrenIdsByParentIds(children, activeOnly);
        }
        return children;
    }

    public int getCountByActiveTrue() {
        return branchRepository.getCountByActiveTrue();
    }

    public String getName(Branch branch, String name) {
        if (branch.getParent() == null) {
            if (StringUtil.isBlank(name)) {
                name = branch.getName();
            }
        } else {
            if (!StringUtil.isBlank(name)) {
                name = branch.getParent().getName() + " > " + name;
            } else {
                name = branch.getParent().getName() + " > " + branch.getName();
            }
            name = this.getName(branch.getParent(), name);
        }
        return name;
    }

    @Override
    public void loadForeignKeys(Branch object) {
    }
}
