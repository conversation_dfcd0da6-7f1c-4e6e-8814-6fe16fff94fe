package com.nymbl.config.utils;

import com.fasterxml.jackson.core.*;
import com.fasterxml.jackson.databind.*;
import com.google.common.base.*;
import com.google.gson.*;
import com.nymbl.config.model.*;
import com.nymbl.tenant.*;
import com.nymbl.tenant.model.*;
import lombok.extern.slf4j.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.*;
import org.jsoup.nodes.*;
import org.springframework.util.*;

import javax.crypto.*;
import javax.crypto.spec.*;
import java.io.*;
import java.util.Objects;
import java.util.*;
import java.util.regex.*;
import java.util.stream.*;

/**
 * Created by <PERSON> on 05/16/2017.
 */
@Slf4j
public class StringUtil {

    public static String SSN_REGEX = "^(\\d{3}-?\\d{2}-?\\d{4}|XXX-XX-XXXX)$";

    public static String PHONE_REGEX = "^(\\d{3}-?\\d{3}-?\\d{4}|XXX-XXX-XXXX)$";

    public static String DOB_REGEX = "^(1[0-2]|0[1-9])(3[01]|[12][0-9]|0[1-9])[0-9]{4}$";

    private static final Gson gson = new Gson();

    public static Boolean isBlank(String string) {
        if (string != null) {
            string = string.replace('\u00A0', ' ').trim();
        }
        return StringUtils.isBlank(string);
    }

    public static Boolean isValidSSN(String value) {
        if (isBlank(value)) return false;
        Pattern p = Pattern.compile(StringUtil.SSN_REGEX);
        Matcher m = p.matcher(value);
        return m.matches();
    }

    public static Boolean isValidPhone(String value) {
        if (isBlank(value)) return false;
        Pattern p = Pattern.compile(StringUtil.PHONE_REGEX);
        Matcher m = p.matcher(value);
        return m.matches();
    }

    public static Boolean isValidDOB(String value) {
        if (isBlank(value)) return false;
        Pattern p = Pattern.compile(StringUtil.DOB_REGEX);
        Matcher m = p.matcher(value);
        return m.matches();
    }

    public static String formatSSN(String value) {
        String result = value.substring(0, 3).concat("-").concat(value.substring(4, 6)).concat("-").concat(value.substring(7, 11));
        return result;
    }

    public static String getAbbreviation(String value) {
        String[] array = value.split("(?<=[\\S])[\\S]*\\s*");
        return String.join("", array);
    }

    @SuppressWarnings("rawtypes")
    public static String join(Collection list, String delimiter) {
        return StringUtils.join(list, delimiter);
    }

    public static String join(String[] list, String delimiter) {
        return StringUtils.join(list, delimiter);
    }

    public static String formatPhone(String value) {
        if (isBlank(value)) return null;
        return value.replaceAll(" ", "-").replaceAll("\\(", "").replaceAll("\\)", "");
    }

    public static String getGender(String value) {
        if (value != null) {
            switch (value) {
                case "Male":
                    return "M";
                case "Female":
                    return "F";
                case "Non-Binary":
                default:
                    return "U";
            }
        } else {
            return "U";
        }
    }

    public static String relationToSubscriber(String value) {
        if (value != null) {
            switch (value) {
                case "self":
                    return "18";
                case "spouse":
                    return "01";
                case "child":
                    return "19";
                default:
                    return "G8";
            }
        } else {
            return "G8";
        }
    }

    public static String formatName(Patient o, String inFormat, boolean commaDelimited) {
        return formatName(new FullName(o, false), inFormat, commaDelimited);
    }

    public static String formatName(Physician o, String inFormat, boolean commaDelimited) {
        return formatName(new FullName(o, false), inFormat, commaDelimited);
    }

    public static String formatName(Contact o, String inFormat, boolean commaDelimited) {
        return formatName(new FullName(o, false), inFormat, commaDelimited);
    }

    public static String formatName(FullName nameParts, String inFormat, boolean commaDelimited) {
        List<String> formats = Arrays.asList("LFM", "LFMC", "LF", "LFC", "FML", "FMLC", "FL", "FLC", "LFMi", "FMiL", "FMiLC", "LFMiC");
        String format = formats.contains(inFormat) ? inFormat : "LFM";
        String name = "";

        String lastname = nameParts.getLastName();
        String firstname = nameParts.getFirstName();
        String middlename = nameParts.getMiddleName();
        String credentials = nameParts.getCredentials();
        String suffix = nameParts.getSuffix();

        if (Arrays.asList("LFM", "LFMC", "FML", "FMLC", "LFMi", "FMiL", "FMiLC", "LFMiC").contains(format)) {
            if (format.equals("LFM")) {
                if (commaDelimited)
                    name = lastname + ", " + firstname + (StringUtil.isBlank(middlename) ? "" : ", " + middlename) + (StringUtil.isBlank(suffix) ? "" : ", " + suffix);
                else
                    name = lastname + ", " + firstname + (StringUtil.isBlank(nameParts.getMiddleName()) ? "" : " " + nameParts.getMiddleName()) + (StringUtil.isBlank(suffix) ? "" : " " + suffix);
            } else if (format.equals("LFMC")) {
                name = lastname + ", " + firstname + (StringUtil.isBlank(nameParts.getMiddleName()) ? "" : " " + nameParts.getMiddleName()) + (StringUtil.isBlank(suffix) ? "" : " " + suffix) + (StringUtil.isBlank(credentials) ? "" : ", " + credentials);
            } else if (format.equals("FML")) {
                name = firstname + ((middlename == null || middlename.equals("")) ? "" : " " + nameParts.getMiddleName()) + " " + lastname + (StringUtil.isBlank(suffix) ? "" : " " + suffix);
            } else if (format.equals("FMLC")) {
                name = firstname + (StringUtil.isBlank(middlename) ? "" : " " + middlename) + " " + lastname + (StringUtil.isBlank(suffix) ? "" : " " + suffix) + (StringUtil.isBlank(credentials) ? "" : " (" + credentials + ")");
            } else if (format.equals("LFMi")) {
                if (commaDelimited)
                    name = lastname + ", " + firstname + (StringUtil.isBlank(middlename) ? "" : ", " + middlename.substring(0, 1) + ". ") + (StringUtil.isBlank(suffix) ? "" : ", " + suffix);
                else
                    name = lastname + ", " + firstname + (StringUtil.isBlank(middlename) ? "" : " " + middlename.substring(0, 1) + ". ") + (StringUtil.isBlank(suffix) ? "" : " " + suffix);
            } else if (format.equals("FMiL")) {
                name = firstname + (StringUtil.isBlank(middlename) ? "" : " " + middlename.substring(0, 1) + ". ") + " " + lastname + (StringUtil.isBlank(suffix) ? "" : " " + suffix);
            } else if (format.equals("FMiLC")) {
                name = firstname + (StringUtil.isBlank(middlename) ? "" : " " + middlename.substring(0, 1) + ". ") + " " + lastname + (StringUtil.isBlank(suffix) ? "" : " " + suffix) + (StringUtil.isBlank(credentials) ? "" : ", " + credentials);
            } else if (format.equals("LFMiC")) {
                name = lastname + ", " + firstname + (StringUtil.isBlank(middlename) ? "" : " " + middlename.substring(0, 1) + ". ") + (StringUtil.isBlank(suffix) ? "" : " " + suffix) + (StringUtil.isBlank(credentials) ? "" : ", " + credentials);
            }
        } else if (Arrays.asList("LF", "LFC", "FL", "FLC").contains(format)) {
            if (format.equals("LF")) {
                name = lastname + ", " + firstname + (StringUtil.isBlank(suffix) ? "" : " " + suffix);
            } else if (format.equals("LFC")) {
                name = lastname + ", " + firstname + (StringUtil.isBlank(suffix) ? "" : " " + suffix) + (StringUtil.isBlank(credentials) ? "" : ", " + credentials);
            } else if (format.equals("FL")) {
                name = firstname + " " + lastname + (StringUtil.isBlank(suffix) ? "" : " " + suffix);
            } else if (format.equals("FLC")) {
                name = firstname + " " + lastname + (StringUtil.isBlank(suffix) ? "" : " " + suffix) + (StringUtil.isBlank(credentials) ? "" : ", " + credentials);
            }
        }
        return StringUtil.isBlank(name) ? "Unnamed" : name;
    }

    public static String formatAddress(FullAddress fullAddress) {
        String result = "";
        if (!isBlank(fullAddress.getStreetAddress())) {
            result = fullAddress.getStreetAddress();
        }
        if (!isBlank(fullAddress.getCity())) {
            if (!isBlank(result)) result = result + ", ";
            result = result + fullAddress.getCity();
        }
        if (!isBlank(fullAddress.getState())) {
            if (!isBlank(result)) result = result + ", ";
            result = result + fullAddress.getState();
        }
        if (!isBlank(fullAddress.getZipcode()))
            result = result + " " + fullAddress.getZipcode();
        return result;
    }

    /**
     * The way full text search works is as follows:
     * 1. In general, punctuation separates "words", so I replace it with spaces
     * 2. "+" in front of a word is equal to "AND", meaning that the word must be present (I use it for every word)
     * 3. "-" in front of a word is equal to "AND NOT", meaning that the word must NOT be present (not used)
     * 4. "*" is a wildcard, I put it at the end of the last word to allow for partial matches
     *    i.e. "+right*" matches both "right" and "righthand" (that must be present)
     * @param keywords
     * @return
     */
    public static String formatKeywordsForFullStringSearch(String keywords) {
        String valOut = null;
        // Sanitize SQL input
        if (!Strings.isNullOrEmpty(keywords) && !keywords.equals("%") && !keywords.equals("%%")) {
            valOut = keywords
                    // remove all non-alphanumeric characters
                    .replaceAll("[^a-zA-Z0-9& ]+", " ")
                    // remove all single-character "words" at the beginning...
                    .replaceAll("^([a-zA-Z0-9&] )+", " ")
                    // ... in the middle
                    .replaceAll(" ([a-zA-Z0-9&] )+", " ")
                    // ... at the end
                    .replaceAll("( [a-zA-Z0-9&])+$", "")
                    // ... or making up the whole string
                    .replaceAll("^[a-zA-Z0-9&]$", "")
                    .trim();
            if (valOut.length() > 2) {
                // format the string like: "+'word1' +'word2' +'word3'..."
                valOut = "+" + valOut.replaceAll("[ ]+", " +") + "*";
            } else {
                valOut = null;
            }
        }
        StringUtil.isBlank("");
        return valOut;
    }

    public static String getWaystarSignature(String key, String params) {
        String HMAC_SHA1_ALGORITHM = "HmacSHA1";
        String valueToHash = params.replaceAll("&", "").replaceAll("=", "");
        SecretKeySpec signingKey = new SecretKeySpec(key.getBytes(), HMAC_SHA1_ALGORITHM);
        Mac mac = null;
        try {
            mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
            mac.init(signingKey);
            return Base64.encodeBase64String(mac.doFinal(valueToHash.getBytes()));
        } catch (Exception e) {
        }
        return null;
    }

    public static String formParseAmount(String amount, boolean isDollars) {
        String result;
        if (amount.contains(".")) {
            int indexOfDecimal = amount.indexOf(".");

            if (isDollars) {
                result = amount.substring(0, indexOfDecimal);
            } else {
                result = amount.substring(indexOfDecimal + 1);
            }
        } else {
            result = "";
        }

        return result;
    }

    public static String format1500Phone(String phone) {
        String result;
        if (phone.length() == 8) {
            result = phone;
        } else {
            result = phone.substring(0, 3) + "-" + phone.substring(3);
        }
        return result;
    }

    public static String formatPhoneForDisplay(String phone) {
        String result = "";
        if (isBlank(phone) || phone.length() < 7) return result;
        if (phone.length() == 7) {
            result = phone.substring(0, 3) + "-" + phone.substring(3);
        } else if (phone.length() == 10) {
            result = phone.substring(0, 3) + "-" + phone.substring(3, 6) + "-" + phone.substring(6);
        }
        return result;
    }

    public static String formatPhoneForDisplayWithExtension(String phone) {
        String result = "";
        if (isBlank(phone) || phone.length() < 7) return result;
        if (phone.length() == 7) {
            result = phone.substring(0, 3) + "-" + phone.substring(3);
        } else if (phone.length() == 10) {
            result = "(" + phone.substring(0, 3) + ") " + phone.substring(3, 6) + "-" + phone.substring(6);
        } else if (phone.length() > 10) {
            result = "(" + phone.substring(0, 3) + ") " + phone.substring(3, 6) + "-" + phone.substring(6, 10) + " Ext." + phone.substring(10);
        }
        return result;
    }

    public static String formatPhoneForEOB(String phone) {
        String result = "";
        if (isBlank(phone) || phone.length() < 7) return result;
        if (phone.length() == 7) {
            result = phone.substring(0, 3) + "-" + phone.substring(3);
        } else if (phone.length() == 10) {
            result = "(" + phone.substring(0, 3) + ")" + phone.substring(3, 6) + "-" + phone.substring(6);
        }
        return result;

    }

    public static String headerFormat(String text) {
        return StringUtils.capitalize(StringUtils.join(StringUtils.splitByCharacterTypeCamelCase(text), ' '));
    }

    //I cannot believe cascade is making me do this.  Not by choice.
    public static String correctMalformedXML(List<String> xmls) {
        String result = "";
        for (String x : xmls) {
            x = x.replaceAll("\n", "");
            result = result + x.trim();
        }
        return result;
    }

    public static StringBuilder stringBuilderReplaceAll(StringBuilder sb, String find, String replace) {
        return new StringBuilder(Pattern.compile(find).matcher(sb).replaceAll(replace));
    }

    public static boolean stringIsNumeric(String str) {
        try {
            int number = Integer.parseInt(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static String getExceptionAsString(Exception ex) {
        StringWriter sw = new StringWriter();
        ex.printStackTrace(new PrintWriter(sw));
        return TenantContext.getCurrentTenant() + " " + sw.toString();
    }

    public static String getEmpireHtmlError(Exception e) {
        Document doc = Jsoup.parse(e.getMessage());
        return doc.getElementsByTag("title").text();
    }

    public static String fillTabs(int count) {
        String t = "";
        for (int i = 0; i < count; i++) {
            t = t.concat("\t");
        }
        return t;
    }

    public static String fillSpaces(int count) {
        String t = "";
        for (int i = 0; i < count; i++) {
            t = t.concat(" ");
        }
        return t;
    }

    public static String toUpperCaseSafe(String s) {
        if (s != null)
            return s.toUpperCase();
        return null;
    }

    public static String returnNAifNullOrEmpty(String s) {
        if (s != null && !s.equals("")) {
            return s;
        }
        return "N/A";
    }

    public static String returnYesNoNA(Boolean b) {
        if (b != null) {
            if (b) {
                return "Yes";
            } else {
                return "No";
            }
        }
        return "N/A";
    }

    public static String getMySSQLConfigurationFile(String activeProfiles) {
        String arch = System.getProperty("os.arch");
        String result = " ";
        if (("amd64".equals(arch) || "aarch64".equals(arch)) && !activeProfiles.contains("local")) {
            result = " --defaults-file=/opt/nymbl/uploads/.my.cnf ";
        }
        return result;
    }

    /**
     * Sanitizes input to prevent SQL injection and XSS attacks.
     * Removes potentially dangerous characters while preserving safe ones.
     *
     * @param input The input string to sanitize
     * @return Sanitized string safe for database storage and display
     */
    public static String sanitizeInput(String input) {
        if (isBlank(input)) {
            return input;
        }

        // Remove SQL injection patterns and XSS patterns
        // Allow only alphanumeric characters, spaces, hyphens, underscores, periods, commas, parentheses, and ampersands
        return input.replaceAll("[^a-zA-Z0-9\\s\\-_.,()&]", "").trim();
    }

    @SuppressWarnings("unchecked")
    public static String getJsonStringValue(Object object) {
        String result = null;
        try {
            if (object instanceof GeneralLedgerStatic) {
                GeneralLedgerStatic transaction = (GeneralLedgerStatic) object;
                Long tid = transaction.getId();
                transaction.setId(null);
                // result = mapper.writeValueAsString(transaction);
                result = gson.toJson(transaction);
                transaction.setId(tid);
            } else if (object instanceof GeneralLedgerLive) {
                GeneralLedgerLive transaction = (GeneralLedgerLive) object;
                Long tid = transaction.getId();
                transaction.setId(null);
                result = gson.toJson(transaction);
                transaction.setId(tid);
            } else {
                ArrayList<Object> objList = (ArrayList<Object>) object;
                if (objList.size() == 0) {
                    result = "";
                } else {
                    Object glObj = objList.get(0);

                    if (glObj instanceof String) {
                        result = gson.toJson(objList);
                    } else if (glObj instanceof GeneralLedgerStatic) {
                        StringBuffer hashBuffer = new StringBuffer();
                        ArrayList<GeneralLedgerStatic> transactions = (ArrayList<GeneralLedgerStatic>) object;
                        result = hashBuffer.toString();
                    }
                }
            }
        } finally {
        }
        return result;
    }

    public static String getStateAbbr(String state) {
        try {
            LinkedHashMap<String, String> statesMap = new ObjectMapper().readValue(ResourceUtils.getURL("classpath:static/scripts/jsons/states.json"), LinkedHashMap.class);
            List<String> m = statesMap.entrySet().stream().filter(s -> Objects.equals(s.getValue(), state)).map(Map.Entry::getKey).collect(Collectors.toList());
            for (Map.Entry<String, String> entry : statesMap.entrySet()) {
                if (entry.getValue().equalsIgnoreCase(state)) {
                    return entry.getKey();
                }
            }
            return null;
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public static String checkCharUpperCase(String input, int index) {
        // Check if the input has at least two characters
        if (input.length() < index) {
            return input; // Return the original string if it's too short
        }
        char charToCheck = input.charAt(index - 1); // Get the second character
        // Check if the second character is uppercase
        if (Character.isUpperCase(charToCheck)) {
            return input; // Return the original string if second character is uppercase
        }
        // Otherwise, return the string with the first character in lowercase
        return Character.toLowerCase(input.charAt(0)) + input.substring(1);
    }

    public static <T> T fromJson(String json, Class<T> clazz) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(json, clazz);
    }

    public static String toJson(Object object) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(object);
    }
}
