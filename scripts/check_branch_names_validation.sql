-- NYM-3200: Branch Name Validation Check Script
-- Purpose: Identify branch names that would violate the new validation rules
-- Usage: Run this script against any tenant database (demo, nymbl_training, etc.)

-- Check for branch names with invalid characters
SELECT
    `id`,
    `name`,
    'INVALID CHARACTERS FOUND' AS `status`,
    'Contains characters not allowed by new validation rules' AS `description`
FROM `demo`.`branch`
WHERE `name` REGEXP '[^a-zA-Z0-9 \\-_.,()&]'
AND `name` IS NOT NULL
AND `name` != ''

UNION ALL

-- Check for empty or null branch names
SELECT
    `id`,
    COALESCE(`name`, 'NULL') AS `name`,
    'EMPTY/NULL NAME' AS `status`,
    'Branch name is required by new validation rules' AS `description`
FROM `demo`.`branch`
WHERE `name` IS NULL OR `name` = ''

ORDER BY `status`, `id`;

-- Summary count of issues
SELECT
    'SUMMARY' AS `report_section`,
    COUNT(*) AS `total_branches_with_issues`,
    SUM(CASE WHEN `name` REGEXP '[^a-zA-Z0-9 \\-_.,()&]' AND `name` IS NOT NULL AND `name` != '' THEN 1 ELSE 0 END) AS `invalid_characters_count`,
    SUM(CASE WHEN `name` IS NULL OR `name` = '' THEN 1 ELSE 0 END) AS `empty_null_count`
FROM `demo`.`branch`;

-- Show all branch names for reference
SELECT
    'ALL BRANCHES' AS `report_section`,
    `id`,
    `name`,
    CASE
        WHEN `name` IS NULL OR `name` = '' THEN 'NEEDS ATTENTION'
        WHEN `name` REGEXP '[^a-zA-Z0-9 \\-_.,()&]' THEN 'NEEDS ATTENTION'
        ELSE 'VALID'
    END AS `validation_status`
FROM `demo`.`branch`
ORDER BY `validation_status` DESC, `name`;
