-- =====================================================
-- Branch Name Validation Check Script
-- =====================================================
-- Purpose: Identify branch names that would violate the new validation rules
--          introduced in NYM-3200 security fix
-- 
-- Usage: Run this script against any tenant database (demo, nymbl_training, etc.)
--        to identify branches that may need cleanup before deploying the security fix
--
-- Validation Rules: Branch names must only contain:
--   - Letters (a-z, A-Z)
--   - Numbers (0-9)
--   - Spaces
--   - Hyphens (-)
--   - Underscores (_)
--   - Periods (.)
--   - Commas (,)
--   - Parentheses ()
--   - Ampersands (&)
-- =====================================================

-- Check for branch names with invalid characters
SELECT 
    id, 
    name,
    'INVALID CHARACTERS FOUND' as status,
    'Contains characters not allowed by new validation rules' as description
FROM branch 
WHERE name REGEXP '[^a-zA-Z0-9 \\-_.,()&]'
AND name IS NOT NULL 
AND name != ''

UNION ALL

-- Check for empty or null branch names
SELECT 
    id, 
    COALESCE(name, 'NULL') as name,
    'EMPTY/NULL NAME' as status,
    'Branch name is required by new validation rules' as description
FROM branch 
WHERE name IS NULL OR name = ''

ORDER BY status, id;

-- Summary count of issues
SELECT 
    'SUMMARY' as report_section,
    COUNT(*) as total_branches_with_issues,
    SUM(CASE WHEN name REGEXP '[^a-zA-Z0-9 \\-_.,()&]' AND name IS NOT NULL AND name != '' THEN 1 ELSE 0 END) as invalid_characters_count,
    SUM(CASE WHEN name IS NULL OR name = '' THEN 1 ELSE 0 END) as empty_null_count
FROM branch;

-- Show all branch names for reference
SELECT 
    'ALL BRANCHES' as report_section,
    id,
    name,
    CASE 
        WHEN name IS NULL OR name = '' THEN 'NEEDS ATTENTION'
        WHEN name REGEXP '[^a-zA-Z0-9 \\-_.,()&]' THEN 'NEEDS ATTENTION'
        ELSE 'VALID'
    END as validation_status
FROM branch
ORDER BY validation_status DESC, name;
